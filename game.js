// 音符小游戏
function useMusicGame(canvasRef, onGameEnd) {
    // 游戏配置
    const config = {
        gameTime: 60,           // 游戏时间
        canvasWidth: 750,       // 画布宽度
        canvasHeight: 1334,     // 画布高度
        energy: 0,              // 初始能量值
        targetEnergy: 100,      // 目标能量值 (达到此值获胜)
        arrowSpeed: 200,        // 箭头上升速度 (像素/秒)
        hitZoneHeight: 80,      // 击中区域高度
        arrowSize: 130,         // 箭头大小
        boxSize: 130,           // 元素区域大小
        buttonSize: 130,        // 按钮大小
        playerSize: 400,        // 玩家大小

        spawnInterval: 1200,    // 箭头生成间隔(毫秒) - 减少生成频率
        hitTolerance: 90,       // 击中容错范围
        arrowSpawnOffset: 350,  // 箭头从底部多少像素开始出现

        // 3个区域的Y轴位置配置
        boxAreaY: 300,          // 上方元素区域Y坐标
        playerAreaY: 630,       // 中间玩家区域Y坐标 (画布高度的一半)
        buttonAreaY: 1200,      // 下方按钮区域Y坐标基准
        effectAreaY: 420,       // 特效显示区域Y坐标 (元素区域和玩家之间)

        // 特效文字配置
        hitText: 'NICE+5',        // 击中文字
        missText: 'MISS-5',      // 未击中文字
        hitColor: '#ffffff',    // 击中文字颜色（白色）
        missColor: '#ffffff',   // 未击中文字颜色（白色）
        hitShadowColor: '#f6e620',    // 击中文字阴影颜色
        missShadowColor: '#c9103e',   // 未击中文字阴影颜色
        hitFontSize: 40,        // 击中文字大小
        missFontSize: 40,       // 未击中文字大小
        effectDuration: 800,    // 击中特效持续时间(毫秒)
        missEffectDuration: 800, // 未击中特效持续时间(毫秒)

        // 连击文字配置
        comboColor: '#ffffff',    // 连击文字颜色（白色）
        comboShadowColor: '#db3097',  // 连击文字阴影颜色
        comboFontSize: 28,        // 连击文字大小
        comboOffsetY: 50,         // 连击文字相对于特效文字的Y偏移
    };

    // 方向配置
    const directions = {
        LEFT: 0,
        UP: 1,
        RIGHT: 2,
        DOWN: 3
    };

    // 游戏状态
    const gameState = ref({
        timeLeft: config.gameTime,
        isRunning: false,
        isGameOver: false,
        energy: config.energy,
        score: 0,
        combo: 0,
        maxCombo: 0,
        arrows: [],             // 上升的箭头
        lastSpawnTime: 0,       // 上次生成箭头的时间
        pressedKeys: new Set(), // 当前按下的按键
        hitEffects: [],         // 击中特效
        missEffects: [],        // 未击中特效
        comboEffects: [],       // 连击特效
        playerDirection: directions.LEFT, // 玩家当前朝向 (默认朝左)
        gameStartTime: 0,       // 游戏开始时间
        progressPercent: 0,     // 进度百分比 (0-100)
    });

    // 图片资源配置
    const imageConfigs = [
        // 元素区域图片
        { key: 'boxLeft', src: 'img/box_left.png', width: config.boxSize },
        { key: 'boxUp', src: 'img/box_left.png', width: config.boxSize },
        { key: 'boxRight', src: 'img/box_left.png', width: config.boxSize },
        { key: 'boxDown', src: 'img/box_left.png', width: config.boxSize },

        // 玩家图片
        { key: 'playerLeft', src: 'img/player_left.png', width: config.playerSize },
        { key: 'playerUp', src: 'img/player_up.png', width: config.playerSize },
        { key: 'playerRight', src: 'img/player_right.png', width: config.playerSize },
        { key: 'playerDown', src: 'img/player_down.png', width: config.playerSize },

        // 按钮图片
        { key: 'arrowLeft', src: 'img/arrow_left.png', width: config.buttonSize },
        { key: 'arrowUp', src: 'img/arrow_left.png', width: config.buttonSize },
        { key: 'arrowRight', src: 'img/arrow_left.png', width: config.buttonSize },
        { key: 'arrowDown', src: 'img/arrow_left.png', width: config.buttonSize },

        // 上升箭头图片
        { key: 'boxfillLeft', src: 'img/arrowfill_left.png', width: config.arrowSize },
        { key: 'boxfillUp', src: 'img/arrowfill_left.png', width: config.arrowSize },
        { key: 'boxfillRight', src: 'img/arrowfill_left.png', width: config.arrowSize },
        { key: 'boxfillDown', src: 'img/arrowfill_left.png', width: config.arrowSize },
    ];

    // 图片资源对象
    const images = {};

    // 音效
    const hitSound = new Howl({
        src: ['./pop1.mp3'],
        volume: 0.8,
        html5: false,
        preload: true,
        pool: 5,
        format: ['mp3']
    });

    const missSound = new Howl({
        src: ['./pop2.mp3'],
        volume: 0.6,
        html5: false,
        preload: true,
        pool: 5,
        format: ['mp3']
    });

    // 预加载并预热音效，减少首次播放延迟
    const preloadSounds = () => {
        // 预热击中音效 - 播放一个极短的静音版本
        hitSound.once('load', () => {
            const tempVolume = hitSound.volume();
            hitSound.volume(0);
            hitSound.play();
            setTimeout(() => {
                hitSound.stop();
                hitSound.volume(tempVolume);
            }, 50);
        });

        // 预热未击中音效 - 播放一个极短的静音版本
        missSound.once('load', () => {
            const tempVolume = missSound.volume();
            missSound.volume(0);
            missSound.play();
            setTimeout(() => {
                missSound.stop();
                missSound.volume(tempVolume);
            }, 50);
        });
    };

    let ctx = null;
    let animationFrame = null;
    let timerInterval = null;

    // 位置计算
    const positions = {
        // 上方元素区域位置 (一排对齐)
        boxes: [
            { x: config.canvasWidth * 0.2, y: config.boxAreaY, direction: directions.LEFT },
            { x: config.canvasWidth * 0.4, y: config.boxAreaY, direction: directions.UP },
            { x: config.canvasWidth * 0.6, y: config.boxAreaY, direction: directions.RIGHT },
            { x: config.canvasWidth * 0.8, y: config.boxAreaY, direction: directions.DOWN }
        ],

        // 中间玩家位置
        player: { x: config.canvasWidth / 2, y: config.playerAreaY },

        // 下方按钮位置 (菱形排列)
        buttons: [
            { x: config.canvasWidth / 2 - 200, y: config.buttonAreaY - 25, direction: directions.LEFT },
            { x: config.canvasWidth / 2, y: config.buttonAreaY - 100, direction: directions.UP },
            { x: config.canvasWidth / 2 + 200, y: config.buttonAreaY - 25, direction: directions.RIGHT },
            { x: config.canvasWidth / 2, y: config.buttonAreaY + 50, direction: directions.DOWN }
        ]
    };

    // 按键映射
    const keyMap = {
        'ArrowLeft': directions.LEFT,
        'KeyA': directions.LEFT,
        'ArrowUp': directions.UP,
        'KeyW': directions.UP,
        'ArrowRight': directions.RIGHT,
        'KeyD': directions.RIGHT,
        'ArrowDown': directions.DOWN,
        'KeyS': directions.DOWN
    };

    // 创建旋转图片
    const createRotatedImage = (sourceImg, rotation, targetWidth, targetHeight) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Canvas尺寸根据旋转角度确定
        if (rotation === 90 || rotation === 270) {
            canvas.width = targetHeight;
            canvas.height = targetWidth;
        } else {
            canvas.width = targetWidth;
            canvas.height = targetHeight;
        }

        ctx.translate(canvas.width / 2, canvas.height / 2);
        ctx.rotate((rotation * Math.PI) / 180);

        // 绘制时使用目标尺寸，确保图片完整显示
        ctx.drawImage(sourceImg, -targetWidth / 2, -targetHeight / 2, targetWidth, targetHeight);

        return canvas;
    };

    // 加载图片资源
    const loadAssets = () => {
        return new Promise((resolve) => {
            // 需要加载的基础图片
            const baseImages = [
                { key: 'box_left', src: 'img/box_left.png' },
                { key: 'arrow_left', src: 'img/arrow_left.png' },
                { key: 'arrowfill_left', src: 'img/arrowfill_left.png' },
                { key: 'playerLeft', src: 'img/player_left.png' },
                { key: 'playerUp', src: 'img/player_up.png' },
                { key: 'playerRight', src: 'img/player_right.png' },
                { key: 'playerDown', src: 'img/player_down.png' },
            ];

            let totalImages = baseImages.length;
            let loadedImages = 0;

            console.log('开始加载图片资源...');
            console.log(`需要加载的图片总数: ${totalImages}`);

            const onLoad = () => {
                loadedImages++;
                console.log(`已加载图片: ${loadedImages}/${totalImages}`);
                if (loadedImages === totalImages) {
                    console.log('所有图片加载完成，开始生成旋转图片...');
                    generateRotatedImages();
                    resolve();
                }
            };

            const generateRotatedImages = () => {
                // 为每个基础图片生成旋转版本
                const rotationConfigs = [
                    { base: 'box_left', keys: ['boxLeft', 'boxUp', 'boxRight', 'boxDown'], size: config.boxSize },
                    { base: 'arrow_left', keys: ['arrowLeft', 'arrowUp', 'arrowRight', 'arrowDown'], size: config.buttonSize },
                    { base: 'arrowfill_left', keys: ['boxfillLeft', 'boxfillUp', 'boxfillRight', 'boxfillDown'], size: config.arrowSize }
                ];

                rotationConfigs.forEach(rotConfig => {
                    const baseImg = images[rotConfig.base];
                    if (baseImg && baseImg.complete) {
                        const aspectRatio = baseImg.height / baseImg.width;
                        const baseWidth = rotConfig.size;
                        const baseHeight = baseWidth * aspectRatio;

                        // 生成4个方向的图片
                        const rotations = [0, 90, 180, 270];
                        rotations.forEach((rotation, index) => {
                            let targetWidth, targetHeight;

                            if (rotation === 90 || rotation === 270) {
                                // 90度和270度旋转：保持主要尺寸为baseWidth，但宽高要互换
                                targetWidth = baseHeight;  // 旋转后的宽度 = 原图高度
                                targetHeight = baseWidth;  // 旋转后的高度 = 原图宽度
                            } else {
                                // 0度和180度旋转：保持原始尺寸
                                targetWidth = baseWidth;
                                targetHeight = baseHeight;
                            }

                            const rotatedImg = createRotatedImage(baseImg, rotation, targetWidth, targetHeight);
                            images[rotConfig.keys[index]] = rotatedImg;

                            // 保存配置信息
                            const config = imageConfigs.find(c => c.key === rotConfig.keys[index]);
                            if (config) {
                                config.width = targetWidth;
                                config.aspectRatio = targetHeight / targetWidth;
                            }
                        });
                    }
                });

                // 处理玩家图片的长宽比
                ['playerLeft', 'playerUp', 'playerRight', 'playerDown'].forEach(key => {
                    const img = images[key];
                    const config = imageConfigs.find(c => c.key === key);
                    if (img && config) {
                        config.aspectRatio = img.height / img.width;
                    }
                });

                console.log('旋转图片生成完成');
            };

            baseImages.forEach((imgConfig) => {
                const img = new Image();
                img.onload = onLoad;
                img.onerror = (e) => {
                    console.error('加载图片失败:', imgConfig.src, e);
                    onLoad(); // 即使失败也要继续
                };
                console.log(`开始加载图片: ${imgConfig.src}`);
                img.src = imgConfig.src;
                images[imgConfig.key] = img;
            });
        });
    };

    // 生成随机箭头
    const spawnArrow = () => {
        const direction = Math.floor(Math.random() * 4);
        const targetBox = positions.boxes[direction];

        const arrow = {
            direction: direction,
            x: targetBox.x, // 使用目标元素区域的x坐标，确保轨道对齐
            y: config.canvasHeight - config.arrowSpawnOffset, // 从距离底部指定像素开始
            targetY: targetBox.y,
            speed: config.arrowSpeed, // 像素/秒
            lastUpdateTime: Date.now(), // 上次更新时间
            hit: false,
            missed: false
        };

        gameState.value.arrows.push(arrow);
    };

    // 更新箭头位置
    const updateArrows = () => {
        const currentTime = Date.now();
        
        // 生成新箭头
        if (currentTime - gameState.value.lastSpawnTime > config.spawnInterval) {
            spawnArrow();
            gameState.value.lastSpawnTime = currentTime;
        }
        
        // 更新现有箭头 (基于时间的移动)
        gameState.value.arrows.forEach((arrow) => {
            const now = Date.now();
            const deltaTime = (now - arrow.lastUpdateTime) / 1000; // 转换为秒
            arrow.y -= arrow.speed * deltaTime; // 像素/秒 * 秒 = 像素
            arrow.lastUpdateTime = now;
            
            // 检查是否超出屏幕顶部 (错过，不扣分不显示特效)
            if (arrow.y < -config.arrowSize && !arrow.hit) {
                arrow.missed = true;
                // 错过只重置连击，不扣能量
                gameState.value.combo = 0;
                // 错过不播放音效，不显示特效
            }
        });
        
        // 移除已处理的箭头
        gameState.value.arrows = gameState.value.arrows.filter(arrow => 
            arrow.y > -config.arrowSize && !arrow.missed
        );
    };

    // 添加击中特效
    const addHitEffect = () => {
        gameState.value.hitEffects.push({
            x: config.canvasWidth / 2, // 居中显示
            y: config.effectAreaY, // 使用配置的特效显示位置
            startTime: Date.now(),
            scale: 1
        });
    };

    // 添加未击中特效
    const addMissEffect = () => {
        gameState.value.missEffects.push({
            x: config.canvasWidth / 2, // 居中显示
            y: config.effectAreaY, // 使用配置的特效显示位置
            startTime: Date.now(),
            scale: 1
        });
    };

    // 添加连击特效
    const addComboEffect = (comboCount) => {
        if (comboCount > 1) { // 只有连击数大于1时才显示
            gameState.value.comboEffects.push({
                x: config.canvasWidth / 2, // 居中显示
                y: config.effectAreaY + config.comboOffsetY, // 在特效文字下方
                startTime: Date.now(),
                scale: 1,
                comboCount: comboCount
            });
        }
    };

    // 检查击中
    const checkHit = (direction) => {
        const targetBox = positions.boxes[direction];

        // 查找该方向的箭头
        for (let i = 0; i < gameState.value.arrows.length; i++) {
            const arrow = gameState.value.arrows[i];

            if (arrow.direction === direction && !arrow.hit && !arrow.missed) {
                // 检查是否在击中区域内
                const distance = Math.abs(arrow.y - targetBox.y);

                if (distance <= config.hitTolerance) {
                    // 击中！
                    arrow.hit = true;

                    // 增加能量和分数
                    gameState.value.energy = Math.min(config.targetEnergy, gameState.value.energy + 5);
                    gameState.value.score += 10;
                    gameState.value.combo++;
                    gameState.value.maxCombo = Math.max(gameState.value.maxCombo, gameState.value.combo);

                    // 设置玩家朝向 (永久切换)
                    gameState.value.playerDirection = direction;

                    // 播放击中音效
                    hitSound.play();

                    // 添加击中特效
                    addHitEffect();

                    // 添加连击特效
                    addComboEffect(gameState.value.combo);

                    // 移除箭头
                    gameState.value.arrows.splice(i, 1);
                    return true;
                }
            }
        }

        // 如果没有击中任何箭头，说明是点错了，显示MISS特效
        // 扣除能量
        gameState.value.energy = Math.max(0, gameState.value.energy - 5);
        // 重置连击
        gameState.value.combo = 0;
        // 播放未击中音效
        missSound.play();
        // 添加点错特效
        addMissEffect();

        return false;
    };

    // 获取图片配置
    const getImageConfig = (key) => {
        return imageConfigs.find(config => config.key === key);
    };

    // 绘制图片（使用长宽比，图片已预先旋转）
    const drawImage = (key, x, y) => {
        const img = images[key];
        const config = getImageConfig(key);

        if (!img || !config) return;

        const width = config.width;
        const height = width * (config.aspectRatio || 1);

        ctx.drawImage(img, x - width / 2, y - height / 2, width, height);
    };

    // 检查箭头是否在击中范围内
    const isArrowInHitZone = (direction) => {
        const targetBox = positions.boxes[direction];

        // 查找该方向的箭头
        for (let i = 0; i < gameState.value.arrows.length; i++) {
            const arrow = gameState.value.arrows[i];

            if (arrow.direction === direction && !arrow.hit && !arrow.missed) {
                // 检查是否在击中区域内
                const distance = Math.abs(arrow.y - targetBox.y);
                if (distance <= config.hitTolerance) {
                    return true;
                }
            }
        }
        return false;
    };

    // 绘制元素区域
    const drawBoxes = () => {
        positions.boxes.forEach((box, index) => {
            const imageKeys = ['boxLeft', 'boxUp', 'boxRight', 'boxDown'];

            // 检查是否有按键按下
            const isPressed = gameState.value.pressedKeys.has(box.direction);

            // 检查是否有箭头在击中范围内
            const canHit = isArrowInHitZone(box.direction);

            // 添加特效
            if (isPressed || canHit) {
                ctx.save();

                if (canHit) {
                    // 可击中特效：强烈脉冲发光
                    const time = Date.now() * 0.008;
                    const pulse = 0.5 + Math.sin(time) * 0.5; // 0-1之间脉冲
                    // const scale = 1 + pulse * 0.1; // 轻微缩放效果 (已禁用)

                    ctx.shadowColor = '#00FF00';
                    ctx.shadowBlur = 50 * pulse; // 更大的发光范围
                    ctx.globalAlpha = 0.7 + pulse * 0.3;

                    // 添加缩放效果
                    ctx.translate(box.x, box.y);
                    // ctx.scale(scale, scale);
                    ctx.translate(-box.x, -box.y);

                    // 添加额外的外发光
                    ctx.shadowOffsetX = 0;
                    ctx.shadowOffsetY = 0;
                } else if (isPressed) {
                    // 按键按下特效
                    ctx.shadowColor = '#FFD700';
                    ctx.shadowBlur = 30;
                    ctx.globalAlpha = 0.9;
                }
            }

            drawImage(imageKeys[index], box.x, box.y);

            if (isPressed || canHit) {
                ctx.restore();
            }
        });
    };

    // 绘制玩家
    const drawPlayer = () => {
        // 根据当前玩家朝向显示对应图片
        const directionKeys = ['playerLeft', 'playerUp', 'playerRight', 'playerDown'];
        const playerKey = directionKeys[gameState.value.playerDirection];

        drawImage(playerKey, positions.player.x, positions.player.y);
    };

    // 绘制按钮
    const drawButtons = () => {
        positions.buttons.forEach((button, index) => {
            const imageKeys = ['arrowLeft', 'arrowUp', 'arrowRight', 'arrowDown'];

            // 检查是否有按键按下，添加按下效果
            const isPressed = gameState.value.pressedKeys.has(button.direction);

            if (isPressed) {
                ctx.save();
                ctx.shadowColor = '#00FF00';
                ctx.shadowBlur = 15;
                ctx.globalAlpha = 0.9;
                ctx.scale(0.95, 0.95); // 轻微缩小表示按下
            }

            drawImage(imageKeys[index], button.x, button.y);

            if (isPressed) {
                ctx.restore();
            }
        });
    };

    // 绘制上升箭头
    const drawArrows = () => {
        gameState.value.arrows.forEach(arrow => {
            if (arrow.hit || arrow.missed) return;

            const imageKeys = ['boxfillLeft', 'boxfillUp', 'boxfillRight', 'boxfillDown'];
            ctx.save();
            drawImage(imageKeys[arrow.direction], arrow.x, arrow.y);
            ctx.restore();
        });
    };

    // 绘制特效
    const drawEffects = () => {
        const currentTime = Date.now();

        // 绘制击中特效
        gameState.value.hitEffects.forEach((effect, index) => {
            const elapsed = currentTime - effect.startTime;
            const duration = config.effectDuration;

            if (elapsed > duration) {
                gameState.value.hitEffects.splice(index, 1);
                return;
            }

            const progress = elapsed / duration;
            const scale = 1 + progress * 0.5;
            const alpha = 1 - progress;

            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.translate(effect.x, effect.y);
            ctx.scale(scale, scale);

            // 绘制击中文字
            ctx.font = `bold ${config.hitFontSize}px Arial`;
            ctx.textAlign = 'center';

            // 绘制阴影边框
            ctx.strokeStyle = config.hitShadowColor;
            ctx.lineWidth = 3;
            ctx.strokeText(config.hitText, 0, 0);

            // 绘制白色文字
            ctx.fillStyle = config.hitColor;
            ctx.fillText(config.hitText, 0, 0);

            ctx.restore();
        });

        // 绘制未击中特效
        gameState.value.missEffects.forEach((effect, index) => {
            const elapsed = currentTime - effect.startTime;
            const duration = config.missEffectDuration;

            if (elapsed > duration) {
                gameState.value.missEffects.splice(index, 1);
                return;
            }

            const progress = elapsed / duration;
            const scale = 1 + progress * 0.3;
            const alpha = 1 - progress;

            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.translate(effect.x, effect.y);
            ctx.scale(scale, scale);

            // 绘制未击中文字
            ctx.font = `bold ${config.missFontSize}px Arial`;
            ctx.textAlign = 'center';

            // 绘制阴影边框
            ctx.strokeStyle = config.missShadowColor;
            ctx.lineWidth = 3;
            ctx.strokeText(config.missText, 0, 0);

            // 绘制白色文字
            ctx.fillStyle = config.missColor;
            ctx.fillText(config.missText, 0, 0);

            ctx.restore();
        });

        // 绘制连击特效
        gameState.value.comboEffects.forEach((effect, index) => {
            const elapsed = currentTime - effect.startTime;
            const duration = config.effectDuration;

            if (elapsed > duration) {
                gameState.value.comboEffects.splice(index, 1);
                return;
            }

            const progress = elapsed / duration;
            const scale = 1 + progress * 0.3;
            const alpha = 1 - progress;

            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.translate(effect.x, effect.y);
            ctx.scale(scale, scale);

            // 绘制连击文字
            ctx.font = `bold ${config.comboFontSize}px Arial`;
            ctx.textAlign = 'center';

            // 绘制阴影边框
            ctx.strokeStyle = config.comboShadowColor;
            ctx.lineWidth = 2;
            ctx.strokeText(`连击X${effect.comboCount}`, 0, 0);

            // 绘制白色文字
            ctx.fillStyle = config.comboColor;
            ctx.fillText(`连击X${effect.comboCount}`, 0, 0);

            ctx.restore();
        });
    };

    // 更新进度百分比
    const updateProgress = () => {
        // 计算进度百分比，目标能量值对应100%
        gameState.value.progressPercent = Math.min(gameState.value.energy / config.targetEnergy * 100, 100);
    };

    // 主绘制函数
    const draw = () => {
        if (!ctx) return;

        // 清空画布
        ctx.clearRect(0, 0, config.canvasWidth, config.canvasHeight);

        // 绘制游戏元素
        drawBoxes();
        drawPlayer();
        drawButtons();
        drawArrows();
        drawEffects();

        // 更新进度百分比
        updateProgress();
    };

    // 游戏主循环
    const gameLoop = () => {
        if (!gameState.value.isRunning) return;

        updateArrows();
        draw();

        // 检查是否达到目标能量值
        if (gameState.value.energy >= config.targetEnergy) {
            endGame();
            return;
        }

        animationFrame = requestAnimationFrame(gameLoop);
    };

    // 开始计时器
    const startTimer = () => {
        if (timerInterval) clearInterval(timerInterval);

        timerInterval = setInterval(() => {
            if (gameState.value.timeLeft > 0 && gameState.value.isRunning) {
                gameState.value.timeLeft--;

                if (gameState.value.timeLeft <= 0) {
                    endGame();
                }
            }
        }, 1000);
    };

    // 按键事件处理
    const handleKeyDown = (e) => {
        if (!gameState.value.isRunning) return;

        const direction = keyMap[e.code];
        if (direction !== undefined) {
            e.preventDefault();

            if (!gameState.value.pressedKeys.has(direction)) {
                gameState.value.pressedKeys.add(direction);
                checkHit(direction);
            }
        }
    };

    const handleKeyUp = (e) => {
        const direction = keyMap[e.code];
        if (direction !== undefined) {
            e.preventDefault();
            gameState.value.pressedKeys.delete(direction);
        }
    };

    // 触摸开始事件处理 (移动端支持)
    const handleTouchStart = (e) => {
        if (!gameState.value.isRunning) return;

        e.preventDefault();
        const touch = e.touches[0];
        const rect = canvasRef.value.getBoundingClientRect();
        const scaleX = config.canvasWidth / rect.width;
        const scaleY = config.canvasHeight / rect.height;
        const x = (touch.clientX - rect.left) * scaleX;
        const y = (touch.clientY - rect.top) * scaleY;

        // 检查点击的是哪个按钮
        positions.buttons.forEach(button => {
            const distance = Math.sqrt(
                Math.pow(x - button.x, 2) + Math.pow(y - button.y, 2)
            );

            if (distance < config.buttonSize / 2) {
                checkHit(button.direction);
            }
        });
    };

    // 触摸结束事件处理 (不执行游戏逻辑，只用于清理)
    const handleTouchEnd = (e) => {
        if (!gameState.value.isRunning) return;
        e.preventDefault();
        // 这里不执行checkHit，避免重复触发
    };

    // 添加事件监听
    const addEvents = () => {
        document.addEventListener('keydown', handleKeyDown);
        document.addEventListener('keyup', handleKeyUp);

        if (canvasRef.value) {
            canvasRef.value.addEventListener('touchstart', handleTouchStart);
            canvasRef.value.addEventListener('touchend', handleTouchEnd);
        }
    };

    // 移除事件监听
    const removeEvents = () => {
        document.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('keyup', handleKeyUp);

        if (canvasRef.value) {
            canvasRef.value.removeEventListener('touchstart', handleTouchStart);
            canvasRef.value.removeEventListener('touchend', handleTouchEnd);
        }

        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
        }

        if (timerInterval) {
            clearInterval(timerInterval);
        }

        hitSound.stop();
        missSound.stop();
    };

    // 初始化游戏
    const initGame = async () => {
        if (!canvasRef.value) return;

        try {
            ctx = canvasRef.value.getContext('2d');

            // 设置画布尺寸
            canvasRef.value.width = config.canvasWidth;
            canvasRef.value.height = config.canvasHeight;

            // 重置游戏状态
            gameState.value = {
                timeLeft: config.gameTime,
                isRunning: false,
                isGameOver: false,
                energy: config.energy,
                score: 0,
                combo: 0,
                maxCombo: 0,
                arrows: [],
                lastSpawnTime: 0,
                pressedKeys: new Set(),
                hitEffects: [],
                missEffects: [],
                comboEffects: [],
                playerDirection: directions.LEFT, // 确保默认朝左
                gameStartTime: 0, // 初始化游戏开始时间
                progressPercent: Math.min(config.energy / config.targetEnergy * 100, 100), // 初始进度
            };

            addEvents();
            draw();

            // 预热音效
            preloadSounds();

            console.log('音符游戏初始化完成');
        } catch (error) {
            console.error('游戏初始化失败:', error);
        }
    };

    // 开始游戏
    const startGame = () => {
        // 检查所有需要的图片是否都已加载
        const requiredImages = [
            'boxLeft', 'boxUp', 'boxRight', 'boxDown',
            'playerLeft', 'playerUp', 'playerRight', 'playerDown',
            'arrowLeft', 'arrowUp', 'arrowRight', 'arrowDown',
            'boxfillLeft', 'boxfillUp', 'boxfillRight', 'boxfillDown'
        ];

        const allImagesLoaded = requiredImages.every(key => {
            const img = images[key];
            return img && (img.complete !== undefined ? img.complete : true); // Canvas对象没有complete属性
        });

        if (!allImagesLoaded) {
            console.error('图片资源未完全加载，无法开始游戏');
            console.log('当前图片状态:', requiredImages.map(key => ({
                key,
                exists: !!images[key],
                complete: images[key]?.complete
            })));
            return;
        }

        gameState.value.isRunning = true;
        gameState.value.isGameOver = false;
        gameState.value.lastSpawnTime = Date.now();
        gameState.value.gameStartTime = Date.now(); // 记录游戏开始时间

        startTimer();
        gameLoop();

        console.log('音符游戏开始');
    };

    // 暂停游戏
    const pauseGame = () => {
        gameState.value.isRunning = false;

        if (timerInterval) {
            clearInterval(timerInterval);
        }

        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
        }
    };

    // 恢复游戏
    const resumeGame = () => {
        if (gameState.value.isGameOver) return;

        gameState.value.isRunning = true;
        gameState.value.lastSpawnTime = Date.now();

        startTimer();
        gameLoop();
    };

    // 结束游戏
    const endGame = () => {
        gameState.value.isRunning = false;
        gameState.value.isGameOver = true;

        if (timerInterval) {
            clearInterval(timerInterval);
        }

        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
        }

        // 分数就是能量值
        const finalScore = gameState.value.energy;
        const maxCombo = gameState.value.maxCombo;

        console.log(`游戏结束 - 能量: ${finalScore}, 最大连击: ${maxCombo}`);

        // 判断游戏胜利条件 - 能量值达到目标值
        const isWin = gameState.value.energy >= config.targetEnergy;

        if (onGameEnd) {
            onGameEnd(isWin, {
                score: finalScore,      // 分数 = 能量值
                maxCombo: maxCombo,
                energy: finalScore,     // 能量值
                totalScore: finalScore  // 总分 = 能量值
            });
        }
    };

    // 重置游戏
    const resetGame = () => {
        pauseGame();

        gameState.value = {
            timeLeft: config.gameTime,
            isRunning: false,
            isGameOver: false,
            energy: config.energy,
            score: 0,
            combo: 0,
            maxCombo: 0,
            arrows: [],
            lastSpawnTime: 0,
            pressedKeys: new Set(),
            hitEffects: [],
            missEffects: [],
            comboEffects: [],
        };

        draw();
    };

    // 获取游戏统计信息
    const getGameStats = () => {
        return {
            score: gameState.value.score,
            combo: gameState.value.combo,
            maxCombo: gameState.value.maxCombo,
            energy: gameState.value.energy,
            timeLeft: gameState.value.timeLeft,
            isRunning: gameState.value.isRunning,
            isGameOver: gameState.value.isGameOver
        };
    };

    return {
        gameState,
        loadAssets,
        initGame,
        startGame,
        pauseGame,
        resumeGame,
        endGame,
        resetGame,
        addEvents,
        removeEvents,
        getGameStats,
        checkHit,
        updateArrows,
        drawBoxes,
        drawPlayer,
        drawButtons,
        drawArrows,
        drawEffects,
        updateProgress,
        draw,
        gameLoop,
        startTimer
    };
}
