// 取消微信浏览器点击的蓝色背景
// *{
//     -webkit-touch-callout:none;
//     -webkit-user-select:none; 
//     -moz-user-select:none;
//     -ms-user-select:none;
//     user-select:none;
//     -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
//     -webkit-user-select: none;
//     -moz-user-focus: none;
//     -moz-user-select: none
// }

@font-face {
    font-family: '思源宋体';
    src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}

// @font-face {
//     font-family: 'TBMCYXT';
//     src: url(https://ztimg.hefei.cc/static/common/fonts/TaoBaoMaiCaiTi-Regular.ttf);
// }

body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
    font-size: 3.5vw
}

@formcolor: #91395d;


.musicbtn {
    width: 10vw;
    height: 10vw;
    top: 3.6vw;
    right: 2.8vw;
    background: url(../img/music.png) no-repeat center center/contain;
    z-index: 11;
}

.warp {
    margin: 0 auto;
    min-height: 170vw;
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: '思源宋体';
    background: linear-gradient(180deg, #261337 0%, #984ab3 100%);

    .swipe_container {
        width: 100%;
        height: 100%;
    }

    .page {
        width: 100%;
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
        color: #91395d;
        background: url(../img/bj.jpg) no-repeat center center/100vw auto;

        .rotation {
            margin-top: 10vw;
            width: 54.6667vw;
            height: 7vw;
            border: 1px solid #fff;
            border-radius: 3.5vw;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 3;
            color: #fff;
            background: #bd4f93;

            .van-swipe {
                height: 8.8vw;

                .van-swipe-item {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                }
            }

            p {
                text-align: center;
                white-space: nowrap;
                font-size: 4vw;

                span {
                    font-size: 4vw;
                }
            }
        }

        .title {
            margin-top: 2vw;
            width: 79.2vw;
            z-index: 2;
        }

        .title2 {
            margin-top: 3vw;
            width: 79.2vw;
            z-index: 3;
        }

        .start {
            margin-top: 50vw;
            width: 26.8vw;
            height: 26.8vw;
            flex-shrink: 0;
            background: url(../img/start.png) no-repeat center center/100% 100%;
            z-index: 2;
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: url(../img/start_area.png) no-repeat center center/100% 100%;
                // 放大光圈动画
                animation: scaleLight 1s ease-in-out infinite;
            }
        }

        .bg2 {
            width: 100vw;
            position: absolute;
            left: 0;
            bottom: 0;
            pointer-events: none;
        }

        .bg3 {
            width: 100vw;
            position: absolute;
            left: 0;
            top: 0;
            pointer-events: none;
        }

        .button_container {
            position: relative;
            z-index: 2;
            margin-top: 5vw;
            display: flex;
            justify-content: space-between;
            width: 100%;
            padding: 0 6vw;

            .button {
                width: 40vw;
            }
        }
        .game_area{
            width: 100vw;
            height: 177.8667vw;
            // position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            .time{
                width: 21.6vw;
                height: 7.8667vw;
                background: url(../img/time.png) no-repeat center center/100% 100%;
                color: #d63067;
                position: absolute;
                top: 10vw;
                left: 5vw;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 5vw;
                padding-left: 6vw;
            }
            .score_area{
                width: 84vw;
                height: 4.267vw;
                border: 1px solid #f1e26f;
                box-shadow: 0px 0px 10px 0px rgba(255, 255, 255, 0.5);
                border-radius: 2.1333vw;
                position: absolute;
                top: 22vw;
                .score{
                    background-color: #cf5cdb;
                    position: absolute;
                    left:0;
                    height: 100%;
                    border-radius: 2.1333vw;
                    display: flex;
                    align-items: center;
                    transition: .3s;
                    &::after{
                        content: '';
                        width: 9.3333vw;
                        height: 9.3333vw;
                        background: url(../img/qiu.png) no-repeat center center/100% 100%;
                        position: absolute;
                        right: -4.6667vw;
                        transition: .3s;
                    }
                }
            }
            canvas{
                width: 100%;
                height: 100%;
            }
        }

    }

    .bj2 {
        background-image: url(../img/bj2.jpg);
    }

    .bj3 {
        background-image: url(../img/bj3.jpg);
    }
}

.blur {
    filter: blur(1vw);
}

.fc {
    justify-content: center;
}

.area {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    background: url(../img/area.png) no-repeat center center/100% 100%;

    .back {
        position: absolute;
        bottom: -4vw;
        width: 40vw;
        margin-right: 4vw;
    }
    .back2 {
        position: absolute;
        bottom: -6vw;
        width: 40vw;
    }

    .submit {
        position: absolute;
        bottom: -15vw;
        width: 40vw;
    }

    .rule {
        width: 100%;
        padding: 0 4vw;
        margin: 11vw 0 10vw;
        flex: 1;
        overflow-y: auto;
        line-height: 1.5;
        text-align: justify;
        letter-spacing: -0.1vw;
        position: relative;
    }

    .prize {
        margin-top: 11vw;
        display: flex;
        flex-direction: column;
        align-items: center;

        .mt5 {
            margin-top: 2vw;
        }

        .info {
            padding: 4vw 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 16vw;
            width: 70vw;

            &:first-child {
                border-bottom: 1px dashed #215444;
            }

            .p2 {
                font-size: 5vw;
                line-height: 7vw;
                max-width: 75vw;
                text-align: center;
            }

            .jptit {
                width: 29.467vw;
                margin-bottom: 2vw;
            }
        }

        .edit {
            width: 40vw;
        }
    }


    .form {
        width: 100%;
        padding: 24vw 5vw 0;
        display: flex;
        flex-direction: column;
        align-items: center;

        .form-item {
            margin-left: 0;
            margin-bottom: 5vw;
            display: flex;
            align-items: center;

            label {
                width: 26vw;
                font-weight: bold;
                font-size: 5.6vw;
                white-space: nowrap;
                color: @formcolor;
                flex-shrink: 0;
            }

            div {

                input {
                    margin-bottom: 3vw;

                }

                input:nth-last-child(1) {
                    margin-bottom: 0;
                }
            }

            .right {
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            input {
                margin-left: 0.8vw;
                padding-left: 2.5333vw;
                width: 50vw;
                height: 7.7333vw;
                border: 1px @formcolor solid;
                flex-shrink: 0;
                // border-radius: 0.8vw;
                opacity: 1;
                color: @formcolor;
                font-size: 5.6vw;

                &::-webkit-input-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }

                &:-moz-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }

                &::-moz-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }

                &:-ms-input-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }
            }

            #getArea {
                opacity: 0;
                position: absolute;
                z-index: -1;
            }

        }

        .form-footer {
            margin-top: -10vw;
            display: flex;
            width: 200%;
            transform: scale(0.5);
            color: @formcolor;

            .fz1 {
                font-size: 6vw;
            }

            p {
                font-size: 6vw;
                line-height: 1.5;
                text-align: justify;
                letter-spacing: 0.3vw;
            }
        }

        .button {
            margin-top: -5vw;
            width: 30.4vw;
        }

        .fs {
            align-items: flex-start;

            label {
                margin-top: 0.5vw;
            }
        }
    }
}

.area1 {
    margin-top: 1vw;
    width: 90.2667vw;
    height: 115.8667vw;
    background: url(../img/area1.png) no-repeat center center/100% 100%;
}

.area2 {
    margin-top: 1vw;
    width: 88.5333vw;
    height: 112.5333vw;
    background: url(../img/area2.png) no-repeat center center/100% 100%;
}

.area3 {
    margin-top: 1vw;
    width: 92.5333vw;
    height: 142.4vw;
    background: url(../img/area3.png) no-repeat center center/100% 100%;
}

.mask {
    z-index: 10;
    position: fixed;
    top: 0;
    left: 0;
    min-height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    align-items: center;
    // background: rgba(18, 45, 29, 0.3);
    // backdrop-filter: blur(4px);
    background: url(../img/mask.png) no-repeat center center/100% 100%;
    transform: translateX(-50%);
    left: 50%;
    color: #d63067;
    .countdown{
        height: 59.2vw;
    }
    .popup {
        margin-top: -1vw;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;

        .back {
            width: 40vw;
            position: absolute;
            bottom: -6vw;
            margin-right: 4vw;
        }

    }

    .popup1 {
        width: 85.7333vw;
        height: 56.6667vw;
        background: url(../img/popup1.png) no-repeat center top / 100% 100%;
    }

    .popup2 {
        width: 85.7333vw;
        height: 56.6667vw;
        background: url(../img/popup2.png) no-repeat center top / 100% 100%;

        .close2 {
            width: 8.2667vw;
            position: absolute;
            top: -3vw;
            right: -3vw;
        }
    }

    .popup3 {
        width: 85.6vw;
        height: 60.8vw;
        background: url(../img/popup3.png) no-repeat center top / 100% 100%;
    }

    .popup4 {
        width: 85.6vw;
        height: 60.8vw;
        background: url(../img/popup4.png) no-repeat center top / 100% 100%;
    }

    .popup5 {
        width: 85.6vw;
        height: 60.8vw;
        background: url(../img/popup5.png) no-repeat center top / 100% 100%;
        padding-top: -4vw;

        .p3 {
            font-size: 9.0667vw;
            white-space: nowrap;
            font-weight: bold;
        }

        .p4 {
            font-size: 7vw;
            white-space: nowrap;
        }
    }

    .popup6 {
        width: 85.6vw;
        height: 60.8vw;
        background: url(../img/popup6.png) no-repeat center top / 100% 100%;
    }
}

@keyframes scaleLight{
    0%, 100%{
        transform: scale(1);
        opacity: 1;
    }
    50%{
        transform: scale(1.6);
        opacity: 0;
    }
}